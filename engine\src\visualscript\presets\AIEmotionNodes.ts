/**
 * 视觉脚本AI情感分析节点
 * 提供情感分析和情感驱动动画相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { AIEmotionAnalysisSystem } from '../../ai/AIEmotionAnalysisSystem';
import { EmotionAnalysisResult } from '../../avatar/ai/EmotionBasedAnimationGenerator';
import { EmotionType } from '../../avatar/ai/AnimationGenerationTypes';
import { FacialAnimationComponent, FacialExpressionType } from '../../avatar/components/FacialAnimationComponent';

/**
 * 情感分析节点
 * 分析文本的情感
 */
export class EmotionAnalysisNode extends AsyncNode {

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'detailed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否返回详细结果',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'emotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主要情感'
    });

    this.addOutput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '情感强度'
    });

    this.addOutput({
      name: 'detailedResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '详细分析结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const detailed = this.getInputValue('detailed') as boolean;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分析情感
      const result = await emotionSystem.analyzeEmotion(text, { detailed });
      
      if (result) {
        // 设置输出值
        this.setOutputValue('emotion', result.primaryEmotion);
        this.setOutputValue('intensity', result.intensity);
        
        if (detailed) {
          this.setOutputValue('detailedResult', result.detailedEmotions);
        }
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('情感分析失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感驱动动画节点
 * 根据情感分析结果驱动角色动画
 */
export class EmotionDrivenAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'emotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '情感类型'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '情感强度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 3.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const emotion = this.getInputValue('emotion') as string;
    const intensity = this.getInputValue('intensity') as number;
    const duration = this.getInputValue('duration') as number;

    // 检查输入值是否有效
    if (!entity || !emotion) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取面部动画组件
      const facialComponent = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;

      let success = false;

      // 应用面部表情
      if (facialComponent) {
        // 根据情感类型设置表情
        const expressionType = this.getExpressionForEmotion(emotion);
        if (expressionType) {
          facialComponent.setExpression(expressionType, intensity, duration);
          success = true;
        }
      }

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发输出流程
      this.triggerFlow('flow');

      return success;
    } catch (error) {
      console.error('应用情感动画失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }

  /**
   * 根据情感类型获取表情类型
   * @param emotion 情感类型
   * @returns 表情类型
   */
  private getExpressionForEmotion(emotion: string): FacialExpressionType | null {
    // 根据情感类型选择合适的表情
    switch (emotion) {
      case EmotionType.HAPPY:
        return FacialExpressionType.HAPPY;
      case EmotionType.SAD:
        return FacialExpressionType.SAD;
      case EmotionType.ANGRY:
        return FacialExpressionType.ANGRY;
      case EmotionType.SURPRISED:
        return FacialExpressionType.SURPRISED;
      case EmotionType.FEARFUL:
        return FacialExpressionType.FEAR;
      case EmotionType.DISGUSTED:
        return FacialExpressionType.DISGUSTED;
      case EmotionType.NEUTRAL:
        return FacialExpressionType.NEUTRAL;
      default:
        return FacialExpressionType.NEUTRAL;
    }
  }


}

/**
 * 情感历史记录节点
 * 获取和管理情感分析历史记录
 */
export class EmotionHistoryNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'limit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '获取记录数量限制',
      defaultValue: 10
    });

    this.addInput({
      name: 'clear',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否清除历史记录',
      defaultValue: false
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'history',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '情感历史记录'
    });

    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '历史记录总数'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const limit = this.getInputValue('limit') as number;
    const clear = this.getInputValue('clear') as boolean;

    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.setOutputValue('history', []);
      this.setOutputValue('count', 0);
      return false;
    }

    try {
      // 如果需要清除历史记录
      if (clear) {
        emotionSystem.clearEmotionHistory();
        this.setOutputValue('history', []);
        this.setOutputValue('count', 0);
        return true;
      }

      // 获取历史记录
      const history = emotionSystem.getEmotionHistory(limit);

      // 设置输出值
      this.setOutputValue('history', history);
      this.setOutputValue('count', history.length);

      return true;
    } catch (error) {
      console.error('获取情感历史记录失败:', error);
      this.setOutputValue('history', []);
      this.setOutputValue('count', 0);
      return false;
    }
  }
}

/**
 * 批量情感分析节点
 * 批量分析多个文本的情感
 */
export class BatchEmotionAnalysisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'texts',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '要分析的文本数组',
      defaultValue: []
    });

    this.addInput({
      name: 'detailed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否返回详细结果',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'results',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '分析结果数组'
    });

    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '成功分析的数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const texts = this.getInputValue('texts') as string[];
    const detailed = this.getInputValue('detailed') as boolean;

    // 检查输入值是否有效
    if (!Array.isArray(texts) || texts.length === 0) {
      this.setOutputValue('results', []);
      this.setOutputValue('count', 0);
      this.triggerFlow('fail');
      return false;
    }

    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.setOutputValue('results', []);
      this.setOutputValue('count', 0);
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 批量分析情感
      const results = await emotionSystem.batchAnalyzeEmotions(texts, { detailed });

      // 设置输出值
      this.setOutputValue('results', results);
      this.setOutputValue('count', results.length);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('批量情感分析失败:', error);
      this.setOutputValue('results', []);
      this.setOutputValue('count', 0);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感转换节点
 * 控制情感之间的平滑过渡
 */
export class EmotionTransitionNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'fromEmotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '起始情感',
      defaultValue: 'neutral'
    });

    this.addInput({
      name: 'toEmotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标情感'
    });

    this.addInput({
      name: 'transitionTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '过渡时间（秒）',
      defaultValue: 2.0
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '目标强度',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const fromEmotion = this.getInputValue('fromEmotion') as string;
    const toEmotion = this.getInputValue('toEmotion') as string;
    const transitionTime = this.getInputValue('transitionTime') as number;
    const intensity = this.getInputValue('intensity') as number;

    // 检查输入值是否有效
    if (!entity || !toEmotion) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取面部动画组件
      const facialComponent = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;

      if (facialComponent) {
        // 先设置起始表情
        const fromExpressionType = this.getExpressionForEmotion(fromEmotion);
        if (fromExpressionType) {
          facialComponent.setExpression(fromExpressionType, 1.0, 0.1);
        }

        // 延迟设置目标表情以实现过渡
        setTimeout(() => {
          const toExpressionType = this.getExpressionForEmotion(toEmotion);
          if (toExpressionType) {
            facialComponent.setExpression(toExpressionType, intensity, transitionTime);
          }
        }, 100);

        this.setOutputValue('success', true);
      } else {
        this.setOutputValue('success', false);
      }

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('情感转换失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }

  /**
   * 根据情感类型获取表情类型
   * @param emotion 情感类型
   * @returns 表情类型
   */
  private getExpressionForEmotion(emotion: string): FacialExpressionType | null {
    switch (emotion) {
      case EmotionType.HAPPY:
        return FacialExpressionType.HAPPY;
      case EmotionType.SAD:
        return FacialExpressionType.SAD;
      case EmotionType.ANGRY:
        return FacialExpressionType.ANGRY;
      case EmotionType.SURPRISED:
        return FacialExpressionType.SURPRISED;
      case EmotionType.FEARFUL:
        return FacialExpressionType.FEAR;
      case EmotionType.DISGUSTED:
        return FacialExpressionType.DISGUSTED;
      case EmotionType.NEUTRAL:
        return FacialExpressionType.NEUTRAL;
      default:
        return FacialExpressionType.NEUTRAL;
    }
  }
}

/**
 * 情感统计分析节点
 * 获取情感分析的统计信息
 */
export class EmotionStatisticsNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出数据插槽
    this.addOutput({
      name: 'totalAnalyses',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总分析次数'
    });

    this.addOutput({
      name: 'emotionCounts',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '各情感出现次数'
    });

    this.addOutput({
      name: 'averageIntensities',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '各情感平均强度'
    });

    this.addOutput({
      name: 'dominantEmotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主导情感'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.setOutputValue('totalAnalyses', 0);
      this.setOutputValue('emotionCounts', {});
      this.setOutputValue('averageIntensities', {});
      this.setOutputValue('dominantEmotion', 'neutral');
      return false;
    }

    try {
      // 获取统计信息
      const statistics = emotionSystem.getEmotionStatistics();

      // 设置输出值
      this.setOutputValue('totalAnalyses', statistics.totalAnalyses);
      this.setOutputValue('emotionCounts', statistics.emotionCounts);
      this.setOutputValue('averageIntensities', statistics.averageIntensities);
      this.setOutputValue('dominantEmotion', statistics.dominantEmotion || 'neutral');

      return true;
    } catch (error) {
      console.error('获取情感统计信息失败:', error);
      this.setOutputValue('totalAnalyses', 0);
      this.setOutputValue('emotionCounts', {});
      this.setOutputValue('averageIntensities', {});
      this.setOutputValue('dominantEmotion', 'neutral');
      return false;
    }
  }
}

/**
 * 情感上下文分析节点
 * 基于上下文信息进行情感分析
 */
export class EmotionContextAnalysisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '上下文信息',
      defaultValue: ''
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '语言类型',
      defaultValue: 'zh'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'emotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主要情感'
    });

    this.addOutput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '情感强度'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;
    const context = this.getInputValue('context') as string;
    const language = this.getInputValue('language') as string;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分析情感（带上下文）
      const result = await emotionSystem.analyzeEmotion(text, {
        context,
        language: language as 'zh' | 'en' | 'auto',
        detailed: true
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('emotion', result.primaryEmotion);
        this.setOutputValue('intensity', result.intensity || result.primaryIntensity);
        this.setOutputValue('confidence', result.confidence || 0.5);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('上下文情感分析失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感事件监听节点
 * 监听情感变化事件
 */
export class EmotionEventListenerNode extends FlowNode {
  private isListening: boolean = false;
  private eventListener?: (...args: any[]) => void;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'startListening',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始监听'
    });

    this.addInput({
      name: 'stopListening',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止监听'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'eventType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'emotionAnalyzed'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'onEvent',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '事件触发时'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'eventData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '事件数据'
    });

    this.addOutput({
      name: 'isListening',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否正在监听'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const eventType = this.getInputValue('eventType') as string;

    // 获取情感分析系统
    const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      return false;
    }

    // 检查触发的流程
    const triggeredFlow = this.getTriggeredFlow();

    if (triggeredFlow === 'startListening' && !this.isListening) {
      // 开始监听
      this.eventListener = (data: any) => {
        this.setOutputValue('eventData', data);
        this.triggerFlow('onEvent');
      };

      emotionSystem.addEventListener(eventType, this.eventListener);
      this.isListening = true;
      this.setOutputValue('isListening', true);

    } else if (triggeredFlow === 'stopListening' && this.isListening) {
      // 停止监听
      if (this.eventListener) {
        emotionSystem.removeEventListener(eventType, this.eventListener);
        this.eventListener = undefined;
      }
      this.isListening = false;
      this.setOutputValue('isListening', false);
    }

    return true;
  }

  /**
   * 获取触发的流程名称
   */
  private getTriggeredFlow(): string | null {
    // 这里需要根据实际的流程触发机制来实现
    // 暂时返回null，实际实现需要根据节点系统的具体API
    return null;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.isListening && this.eventListener) {
      const emotionSystem = this.context.world.getSystem(AIEmotionAnalysisSystem);
      if (emotionSystem) {
        const eventType = this.getInputValue('eventType') as string;
        emotionSystem.removeEventListener(eventType, this.eventListener);
      }
    }
    super.dispose();
  }
}

/**
 * 注册AI情感节点
 * @param registry 节点注册表
 */
export function registerAIEmotionNodes(registry: NodeRegistry): void {
  // 注册情感分析节点
  registry.registerNodeType({
    type: 'ai/emotion/analyze',
    category: NodeCategory.AI,
    constructor: EmotionAnalysisNode,
    label: '情感分析',
    description: '分析文本的情感',
    icon: 'emotion',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'analysis']
  });

  // 注册情感驱动动画节点
  registry.registerNodeType({
    type: 'ai/emotion/driveAnimation',
    category: NodeCategory.AI,
    constructor: EmotionDrivenAnimationNode,
    label: '情感驱动动画',
    description: '根据情感分析结果驱动角色动画',
    icon: 'animation',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'animation']
  });

  // 注册情感历史记录节点
  registry.registerNodeType({
    type: 'ai/emotion/history',
    category: NodeCategory.AI,
    constructor: EmotionHistoryNode,
    label: '情感历史记录',
    description: '获取和管理情感分析历史记录',
    icon: 'history',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'history']
  });

  // 注册情感统计分析节点
  registry.registerNodeType({
    type: 'ai/emotion/statistics',
    category: NodeCategory.AI,
    constructor: EmotionStatisticsNode,
    label: '情感统计分析',
    description: '获取情感分析的统计信息',
    icon: 'chart',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'statistics']
  });

  // 注册批量情感分析节点
  registry.registerNodeType({
    type: 'ai/emotion/batchAnalyze',
    category: NodeCategory.AI,
    constructor: BatchEmotionAnalysisNode,
    label: '批量情感分析',
    description: '批量分析多个文本的情感',
    icon: 'batch',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'batch', 'analysis']
  });

  // 注册情感转换节点
  registry.registerNodeType({
    type: 'ai/emotion/transition',
    category: NodeCategory.AI,
    constructor: EmotionTransitionNode,
    label: '情感转换',
    description: '控制情感之间的平滑过渡',
    icon: 'transition',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'transition', 'animation']
  });

  // 注册情感上下文分析节点
  registry.registerNodeType({
    type: 'ai/emotion/contextAnalyze',
    category: NodeCategory.AI,
    constructor: EmotionContextAnalysisNode,
    label: '情感上下文分析',
    description: '基于上下文信息进行情感分析',
    icon: 'context',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'context', 'analysis']
  });

  // 注册情感事件监听节点
  registry.registerNodeType({
    type: 'ai/emotion/eventListener',
    category: NodeCategory.AI,
    constructor: EmotionEventListenerNode,
    label: '情感事件监听',
    description: '监听情感变化事件',
    icon: 'listener',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'event', 'listener']
  });
}
