/**
 * 时间节点测试
 */

import {
  GetTimeNode,
  DelayNode,
  TimerNode,
  IntervalNode,
  TimeCompareNode,
  TimeFormatNode,
  TimeScaleNode,
  TimeInterpolateNode,
  TimeSchedulerNode
} from '../TimeNodes';
import { Time } from '../../../utils/Time';

// Jest全局函数声明
declare global {
  function describe(name: string, fn: () => void): void;
  function it(name: string, fn: () => void): void;
  function beforeEach(fn: () => void): void;
  function expect(actual: any): {
    toBe(expected: any): void;
    toHaveProperty(property: string): void;
    toBeUndefined(): void;
    toHaveBeenCalled(): void;
    toHaveBeenCalledWith(...args: any[]): void;
  };
  namespace expect {
    function any(constructor: any): any;
  }
  var jest: {
    fn(): {
      mockReturnValue(value: any): void;
    };
    useFakeTimers(): void;
    clearAllMocks(): void;
    clearAllTimers(): void;
  };
  var global: any;
  function setInterval(callback: () => void, ms: number): any;
  function clearInterval(id: any): void;
  function setTimeout(callback: () => void, ms: number): any;
  function clearTimeout(id: any): void;
  function requestAnimationFrame(callback: () => void): number;
}

// 模拟 performance.now()
const mockPerformanceNow = jest.fn();
global.performance = { now: mockPerformanceNow } as any;

// 模拟 setTimeout 和 clearTimeout
jest.useFakeTimers();

// 模拟 setInterval 和 clearInterval
const mockSetInterval = jest.fn();
const mockClearInterval = jest.fn();
global.setInterval = mockSetInterval;
global.clearInterval = mockClearInterval;

describe('TimeNodes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    mockPerformanceNow.mockReturnValue(1000);
    mockSetInterval.mockReturnValue(123); // 模拟返回定时器ID
    mockClearInterval.mockReturnValue(undefined);

    // 重置Time系统
    Time.initialize();
  });

  describe('GetTimeNode', () => {
    it('应该返回当前时间信息', () => {
      // 初始化Time系统
      Time.initialize();
      Time.update(0.016); // 模拟60FPS的一帧

      const node = new GetTimeNode();
      const result = node.execute();

      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('gameTime');
      expect(result).toHaveProperty('realTime');
      expect(result).toHaveProperty('deltaTime');
      expect(result).toHaveProperty('unscaledDeltaTime');
      expect(result).toHaveProperty('timeScale');
      expect(result).toHaveProperty('frameCount');
      expect(result).toHaveProperty('fps');

      // 验证返回值类型
      expect(typeof result.time).toBe('number');
      expect(typeof result.gameTime).toBe('number');
      expect(typeof result.realTime).toBe('number');
      expect(typeof result.deltaTime).toBe('number');
      expect(typeof result.unscaledDeltaTime).toBe('number');
      expect(typeof result.timeScale).toBe('number');
      expect(typeof result.frameCount).toBe('number');
      expect(typeof result.fps).toBe('number');
    });
  });

  describe('DelayNode', () => {
    it('应该在指定时间后完成', () => {
      const node = new DelayNode();

      // 触发延迟
      let result = node.execute({ trigger: true, duration: 1000 });
      expect(result.progress).toBe(0);
      expect(result.remaining).toBe(1000);

      // 模拟时间过去500ms
      mockPerformanceNow.mockReturnValue(1500);
      result = node.execute({ duration: 1000 }); // 需要传入duration参数
      expect(result.progress).toBe(0.5);
      expect(result.remaining).toBe(500);

      // 模拟时间过去1000ms
      mockPerformanceNow.mockReturnValue(2000);
      result = node.execute({ duration: 1000 }); // 需要传入duration参数
      expect(result.completed).toBe(true);
      expect(result.progress).toBe(1);
      expect(result.remaining).toBe(0);
    });

    it('应该支持重置功能', () => {
      const node = new DelayNode();

      // 触发延迟
      node.execute({ trigger: true, duration: 1000 });

      // 重置
      const result = node.execute({ reset: true });
      expect(result.progress).toBe(0);
      expect(result.remaining).toBe(0);
    });
  });

  describe('TimerNode', () => {
    it('应该正确计时', () => {
      const node = new TimerNode();

      // 开始计时
      let result = node.execute({ start: true });
      expect(result.isRunning).toBe(true);
      expect(result.isPaused).toBe(false);
      expect(result.elapsed).toBe(0); // 刚开始应该是0

      // 模拟时间过去500ms
      mockPerformanceNow.mockReturnValue(1500);
      result = node.execute({});
      expect(result.elapsed).toBe(500);
      expect(result.elapsedSeconds).toBe(0.5);

      // 停止计时
      result = node.execute({ stop: true });
      expect(result.isRunning).toBe(false);
    });

    it('应该支持暂停和恢复', () => {
      const node = new TimerNode();

      // 开始计时
      node.execute({ start: true });

      // 模拟时间过去500ms后暂停
      mockPerformanceNow.mockReturnValue(1500);
      let result = node.execute({ pause: true });
      expect(result.isPaused).toBe(true);
      expect(result.elapsed).toBe(500);

      // 模拟时间再过去500ms（暂停期间）
      mockPerformanceNow.mockReturnValue(2000);
      result = node.execute({});
      expect(result.elapsed).toBe(500); // 应该还是500ms

      // 恢复计时
      result = node.execute({ resume: true });
      expect(result.isPaused).toBe(false);

      // 模拟时间再过去300ms
      mockPerformanceNow.mockReturnValue(2300);
      result = node.execute({});
      expect(result.elapsed).toBe(800); // 500 + 300
    });
  });

  describe('IntervalNode', () => {
    it('应该按间隔执行', () => {
      const node = new IntervalNode();

      // 开始间隔执行
      const result = node.execute({ start: true, interval: 1000 });
      expect(result.isRunning).toBe(true);
      expect(result.count).toBe(0);

      // 验证定时器被设置
      expect(mockSetInterval).toHaveBeenCalledWith(expect.any(Function), 1000);
    });

    it('应该支持最大执行次数限制', () => {
      const node = new IntervalNode();

      // 开始间隔执行，最多执行3次
      node.execute({ start: true, interval: 1000, maxExecutions: 3 });

      // 验证定时器被设置
      expect(mockSetInterval).toHaveBeenCalled();
    });

    it('应该能够停止间隔执行', () => {
      const node = new IntervalNode();

      // 开始间隔执行
      node.execute({ start: true, interval: 1000 });

      // 停止间隔执行
      const result = node.execute({ stop: true });
      expect(result.isRunning).toBe(false);

      // 验证清除定时器被调用
      expect(mockClearInterval).toHaveBeenCalled();
    });
  });

  describe('TimeCompareNode', () => {
    it('应该正确比较时间', () => {
      const node = new TimeCompareNode();
      
      // 测试相等
      let result = node.execute({ timeA: 1000, timeB: 1000, tolerance: 10 });
      expect(result.isEqual).toBe(true);
      expect(result.isGreater).toBe(false);
      expect(result.isLess).toBe(false);
      expect(result.difference).toBe(0);

      // 测试大于
      result = node.execute({ timeA: 1500, timeB: 1000, tolerance: 10 });
      expect(result.isEqual).toBe(false);
      expect(result.isGreater).toBe(true);
      expect(result.isLess).toBe(false);
      expect(result.difference).toBe(500);

      // 测试小于
      result = node.execute({ timeA: 500, timeB: 1000, tolerance: 10 });
      expect(result.isEqual).toBe(false);
      expect(result.isGreater).toBe(false);
      expect(result.isLess).toBe(true);
      expect(result.difference).toBe(-500);
    });
  });

  describe('TimeFormatNode', () => {
    it('应该正确格式化时间', () => {
      const node = new TimeFormatNode();
      
      // 测试 MM:SS 格式
      let result = node.execute({ time: 125000, format: 'MM:SS' }); // 2分5秒
      expect(result.formatted).toBe('02:05');
      expect(result.minutes).toBe(2);
      expect(result.seconds).toBe(5);

      // 测试 HH:MM:SS 格式
      result = node.execute({ time: 3725000, format: 'HH:MM:SS' }); // 1小时2分5秒
      expect(result.formatted).toBe('01:02:05');
      expect(result.hours).toBe(1);

      // 测试秒格式
      result = node.execute({ time: 1500, format: 'seconds' });
      expect(result.formatted).toBe('1.50');
    });
  });

  describe('TimeInterpolateNode', () => {
    it('应该正确进行时间插值', () => {
      const node = new TimeInterpolateNode();

      // 触发插值
      let result = node.execute({
        trigger: true,
        from: 0,
        to: 100,
        duration: 1000,
        easing: 'linear'
      });
      expect(result.isRunning).toBe(true);
      expect(result.value).toBe(0);
      expect(result.progress).toBe(0);

      // 模拟时间过去500ms
      mockPerformanceNow.mockReturnValue(1500);
      result = node.execute({ easing: 'linear' }); // 需要传入easing参数
      expect(result.value).toBe(50); // 线性插值中点
      expect(result.progress).toBe(0.5);

      // 模拟时间过去1000ms
      mockPerformanceNow.mockReturnValue(2000);
      result = node.execute({ easing: 'linear' }); // 需要传入easing参数
      expect(result.value).toBe(100);
      expect(result.progress).toBe(1);
      expect(result.completed).toBe(true);
      expect(result.isRunning).toBe(false);
    });

    it('应该支持重置功能', () => {
      const node = new TimeInterpolateNode();

      // 触发插值
      node.execute({ trigger: true, from: 0, to: 100, duration: 1000 });

      // 重置
      const result = node.execute({ reset: true });
      expect(result.value).toBe(0);
      expect(result.progress).toBe(0);
      expect(result.isRunning).toBe(false);
    });

    it('应该支持不同的缓动类型', () => {
      const node = new TimeInterpolateNode();

      // 测试ease-in缓动
      node.execute({
        trigger: true,
        from: 0,
        to: 100,
        duration: 1000,
        easing: 'easein'
      });

      // 模拟时间过去500ms
      mockPerformanceNow.mockReturnValue(1500);
      const result = node.execute({ easing: 'easein' });

      // ease-in在中点应该小于线性插值的值
      expect(result.value).toBe(25); // 0.5 * 0.5 * 100 = 25
      expect(result.progress).toBe(0.25); // 0.5 * 0.5 = 0.25
    });
  });

  describe('TimeSchedulerNode', () => {
    it('应该正确调度事件', () => {
      const node = new TimeSchedulerNode();
      
      // 添加事件
      node.execute({ addEvent: true, eventTime: 1000, eventData: 'test' });
      
      // 开始调度
      let result = node.execute({ start: true });
      expect(result.isRunning).toBe(true);
      expect(result.currentTime).toBe(0);

      // 模拟时间过去500ms
      mockPerformanceNow.mockReturnValue(1500);
      result = node.execute({});
      expect(result.currentTime).toBe(500);
      expect(result.eventTriggered).toBeUndefined();

      // 模拟时间过去1000ms，事件应该触发
      mockPerformanceNow.mockReturnValue(2000);
      result = node.execute({});
      expect(result.currentTime).toBe(1000);
      expect(result.eventTriggered).toBe(true);
      expect(result.eventData).toBe('test');
    });

    it('应该支持重置功能', () => {
      const node = new TimeSchedulerNode();
      
      // 添加事件并开始
      node.execute({ addEvent: true, eventTime: 1000, eventData: 'test' });
      node.execute({ start: true });
      
      // 重置
      const result = node.execute({ reset: true });
      expect(result.isRunning).toBe(false);
      expect(result.currentTime).toBe(0);
    });
  });
});
