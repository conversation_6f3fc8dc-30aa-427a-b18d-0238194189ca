/**
 * 视觉脚本AI自然语言处理节点
 * 提供文本分类、命名实体识别、文本摘要等功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AIModelManager } from '../../ai/AIModelManager';

/**
 * 文本分类节点
 * 对文本进行分类
 */
export class TextClassificationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分类的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'categories',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '可选的分类类别',
      defaultValue: []
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'category',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '分类结果'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });

    this.addOutput({
      name: 'allCategories',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '所有分类结果及置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const categories = this.getInputValue('categories') as string[];

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取文本分类模型
      const model = aiModelManager.getModel('text-classification');
      if (!model || !model.classifyText) {
        this.triggerFlow('fail');
        return false;
      }

      // 分类文本
      const result = await model.classifyText(text, categories);
      
      if (result) {
        // 设置输出值
        this.setOutputValue('category', result.label);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('allCategories', result.allLabels);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本分类失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
export class NamedEntityRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要识别的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'entityTypes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '要识别的实体类型',
      defaultValue: []
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '识别到的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const entityTypes = this.getInputValue('entityTypes') as string[];

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取命名实体识别模型
      const model = aiModelManager.getModel('named-entity-recognition');
      if (!model || !model.recognizeEntities) {
        this.triggerFlow('fail');
        return false;
      }

      // 识别命名实体
      const result = await model.recognizeEntities(text);
      
      if (result) {
        // 设置输出值
        this.setOutputValue('entities', result.entities);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('命名实体识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本摘要节点
 * 生成文本摘要
 */
export class TextSummaryNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要摘要的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'maxLength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大摘要长度',
      defaultValue: 100
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'summary',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的摘要'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const maxLength = this.getInputValue('maxLength') as number;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取文本摘要模型
      const model = aiModelManager.getModel('text-summarization');
      if (!model || !model.summarizeText) {
        this.triggerFlow('fail');
        return false;
      }

      // 生成摘要
      const result = await model.summarizeText(text, maxLength);
      
      if (result) {
        // 设置输出值
        this.setOutputValue('summary', result.summary);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成摘要失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 语言翻译节点
 * 将文本从一种语言翻译为另一种语言
 */
export class LanguageTranslationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要翻译的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'sourceLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '源语言代码',
      defaultValue: 'auto'
    });

    this.addInput({
      name: 'targetLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标语言代码',
      defaultValue: 'zh'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '翻译成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '翻译失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'translatedText',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '翻译后的文本'
    });

    this.addOutput({
      name: 'sourceLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '检测到的源语言'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '翻译置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const sourceLanguage = this.getInputValue('sourceLanguage') as string;
    const targetLanguage = this.getInputValue('targetLanguage') as string;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取翻译模型
      const model = aiModelManager.getModel('translation');
      if (!model || !model.translateText) {
        this.triggerFlow('fail');
        return false;
      }

      // 翻译文本
      const result = await model.translateText(text, sourceLanguage, targetLanguage);

      if (result) {
        // 设置输出值
        this.setOutputValue('translatedText', result.translatedText);
        this.setOutputValue('sourceLanguage', result.sourceLanguage);
        this.setOutputValue('confidence', result.confidence);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('翻译失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 语音识别节点
 * 将语音转换为文本
 */
export class SpeechRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioData',
      type: SocketType.DATA,
      dataType: 'ArrayBuffer',
      direction: SocketDirection.INPUT,
      description: '音频数据'
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '识别语言',
      defaultValue: 'zh-CN'
    });

    this.addInput({
      name: 'continuous',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '连续识别',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '识别的文本'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '识别置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const audioData = this.getInputValue('audioData') as ArrayBuffer;
    const language = this.getInputValue('language') as string;
    const continuous = this.getInputValue('continuous') as boolean;

    // 检查输入值是否有效
    if (!audioData) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取语音识别模型
      const model = aiModelManager.getModel('speech-recognition');
      if (!model || !model.recognizeSpeech) {
        this.triggerFlow('fail');
        return false;
      }

      // 识别语音
      const result = await model.recognizeSpeech(audioData, {
        language,
        continuous
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('text', result.text);
        this.setOutputValue('confidence', result.confidence);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('语音识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 语音合成节点
 * 将文本转换为语音
 */
export class SpeechSynthesisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要合成的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'voice',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '语音类型',
      defaultValue: 'zh-CN-XiaoxiaoNeural'
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '语速',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'pitch',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音调',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '合成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '合成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'audioData',
      type: SocketType.DATA,
      dataType: 'ArrayBuffer',
      direction: SocketDirection.OUTPUT,
      description: '合成的音频数据'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '音频时长（秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const voice = this.getInputValue('voice') as string;
    const speed = this.getInputValue('speed') as number;
    const pitch = this.getInputValue('pitch') as number;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取语音合成模型
      const model = aiModelManager.getModel('speech-synthesis');
      if (!model || !model.synthesizeSpeech) {
        this.triggerFlow('fail');
        return false;
      }

      // 合成语音
      const result = await model.synthesizeSpeech(text, {
        voice,
        speed,
        pitch
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('audioData', result.audioData);
        this.setOutputValue('duration', result.duration);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('语音合成失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 意图识别节点
 * 识别用户输入的意图
 */
export class IntentRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '上下文信息',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'intent',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '识别的意图'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });

    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '提取的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const context = this.getInputValue('context') as object;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取意图识别模型
      const model = aiModelManager.getModel('intent-recognition');
      if (!model || !model.recognizeIntent) {
        this.triggerFlow('fail');
        return false;
      }

      // 识别意图
      const result = await model.recognizeIntent(text, context);

      if (result) {
        // 设置输出值
        this.setOutputValue('intent', result.intent);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('entities', result.entities);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('意图识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 对话管理节点
 * 管理多轮对话状态
 */
export class DialogueManagementNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'userInput',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户输入',
      defaultValue: ''
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID',
      defaultValue: ''
    });

    this.addInput({
      name: 'userId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '系统回复'
    });

    this.addOutput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '对话上下文'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const userInput = this.getInputValue('userInput') as string;
    const sessionId = this.getInputValue('sessionId') as string;
    const userId = this.getInputValue('userId') as string;

    // 检查输入值是否有效
    if (!userInput || !sessionId) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取对话管理模型
      const model = aiModelManager.getModel('dialogue-management');
      if (!model || !model.processDialogue) {
        this.triggerFlow('fail');
        return false;
      }

      // 处理对话
      const result = await model.processDialogue(userInput, sessionId, userId);

      if (result) {
        // 设置输出值
        this.setOutputValue('response', result.response);
        this.setOutputValue('context', result.context);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('对话管理失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 知识图谱查询节点
 * 查询知识图谱获取相关信息
 */
export class KnowledgeGraphQueryNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'query',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '查询语句',
      defaultValue: ''
    });

    this.addInput({
      name: 'graphId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '知识图谱ID',
      defaultValue: ''
    });

    this.addInput({
      name: 'maxResults',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大结果数',
      defaultValue: 10
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '查询成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '查询失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'results',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '查询结果'
    });

    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '结果数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const query = this.getInputValue('query') as string;
    const graphId = this.getInputValue('graphId') as string;
    const maxResults = this.getInputValue('maxResults') as number;

    // 检查输入值是否有效
    if (!query) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取知识图谱模型
      const model = aiModelManager.getModel('knowledge-graph');
      if (!model || !model.queryKnowledgeGraph) {
        this.triggerFlow('fail');
        return false;
      }

      // 查询知识图谱
      const result = await model.queryKnowledgeGraph(query, {
        graphId,
        maxResults
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('results', result.results);
        this.setOutputValue('count', result.results.length);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('知识图谱查询失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 问答系统节点
 * 基于知识库的问答
 */
export class QuestionAnsweringNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'question',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '问题',
      defaultValue: ''
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '上下文文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'knowledgeBaseId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '知识库ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '回答成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '回答失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'answer',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '答案'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });

    this.addOutput({
      name: 'sources',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '信息来源'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const question = this.getInputValue('question') as string;
    const context = this.getInputValue('context') as string;
    const knowledgeBaseId = this.getInputValue('knowledgeBaseId') as string;

    // 检查输入值是否有效
    if (!question) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取问答模型
      const model = aiModelManager.getModel('question-answering');
      if (!model || !model.answerQuestion) {
        this.triggerFlow('fail');
        return false;
      }

      // 回答问题
      const result = await model.answerQuestion(question, {
        context,
        knowledgeBaseId
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('answer', result.answer);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('sources', result.sources);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('问答失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 关键词提取节点
 * 从文本中提取关键词
 */
export class KeywordExtractionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要提取关键词的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'maxKeywords',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大关键词数量',
      defaultValue: 10
    });

    this.addInput({
      name: 'minScore',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最小重要性分数',
      defaultValue: 0.1
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '提取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '提取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'keywords',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '提取的关键词'
    });

    this.addOutput({
      name: 'scores',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '关键词重要性分数'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const maxKeywords = this.getInputValue('maxKeywords') as number;
    const minScore = this.getInputValue('minScore') as number;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取关键词提取模型
      const model = aiModelManager.getModel('keyword-extraction');
      if (!model || !model.extractKeywords) {
        this.triggerFlow('fail');
        return false;
      }

      // 提取关键词
      const result = await model.extractKeywords(text, {
        maxKeywords,
        minScore
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('keywords', result.keywords);
        this.setOutputValue('scores', result.scores);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('关键词提取失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本相似度计算节点
 * 计算两个文本之间的相似度
 */
export class TextSimilarityNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text1',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '第一个文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'text2',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '第二个文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'method',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '相似度计算方法',
      defaultValue: 'cosine'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '计算成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '计算失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'similarity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '相似度分数'
    });

    this.addOutput({
      name: 'details',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '详细计算结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text1 = this.getInputValue('text1') as string;
    const text2 = this.getInputValue('text2') as string;
    const method = this.getInputValue('method') as string;

    // 检查输入值是否有效
    if (!text1 || !text2) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取文本相似度模型
      const model = aiModelManager.getModel('text-similarity');
      if (!model || !model.calculateSimilarity) {
        this.triggerFlow('fail');
        return false;
      }

      // 计算相似度
      const result = await model.calculateSimilarity(text1, text2, {
        method
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('similarity', result.similarity);
        this.setOutputValue('details', result.details);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本相似度计算失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 语言检测节点
 * 自动检测文本的语言
 */
export class LanguageDetectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要检测的文本',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '检测成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '检测失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '检测到的语言代码'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '检测置信度'
    });

    this.addOutput({
      name: 'allLanguages',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '所有可能的语言及置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取语言检测模型
      const model = aiModelManager.getModel('language-detection');
      if (!model || !model.detectLanguage) {
        this.triggerFlow('fail');
        return false;
      }

      // 检测语言
      const result = await model.detectLanguage(text);

      if (result) {
        // 设置输出值
        this.setOutputValue('language', result.language);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('allLanguages', result.allLanguages);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('语言检测失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本纠错节点
 * 自动纠正文本中的错误
 */
export class TextCorrectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要纠错的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '文本语言',
      defaultValue: 'zh-CN'
    });

    this.addInput({
      name: 'correctionLevel',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '纠错级别',
      defaultValue: 'moderate'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '纠错成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '纠错失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'correctedText',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '纠错后的文本'
    });

    this.addOutput({
      name: 'corrections',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '纠错详情'
    });

    this.addOutput({
      name: 'errorCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '错误数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const language = this.getInputValue('language') as string;
    const correctionLevel = this.getInputValue('correctionLevel') as string;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取文本纠错模型
      const model = aiModelManager.getModel('text-correction');
      if (!model || !model.correctText) {
        this.triggerFlow('fail');
        return false;
      }

      // 纠正文本
      const result = await model.correctText(text, {
        language,
        correctionLevel
      });

      if (result) {
        // 设置输出值
        this.setOutputValue('correctedText', result.correctedText);
        this.setOutputValue('corrections', result.corrections);
        this.setOutputValue('errorCount', result.corrections.length);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本纠错失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册AI自然语言处理节点
 * @param registry 节点注册表
 */
export function registerAINLPNodes(registry: NodeRegistry): void {
  // 注册文本分类节点
  registry.registerNodeType({
    type: 'ai/nlp/classifyText',
    category: NodeCategory.AI,
    constructor: TextClassificationNode,
    label: '文本分类',
    description: '对文本进行分类',
    icon: 'classify',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'classification']
  });

  // 注册命名实体识别节点
  registry.registerNodeType({
    type: 'ai/nlp/recognizeEntities',
    category: NodeCategory.AI,
    constructor: NamedEntityRecognitionNode,
    label: '命名实体识别',
    description: '识别文本中的命名实体',
    icon: 'entity',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'entity', 'recognition']
  });

  // 注册文本摘要节点
  registry.registerNodeType({
    type: 'ai/nlp/generateSummary',
    category: NodeCategory.AI,
    constructor: TextSummaryNode,
    label: '生成文本摘要',
    description: '生成文本摘要',
    icon: 'summary',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'summary']
  });

  // 注册语言翻译节点
  registry.registerNodeType({
    type: 'ai/nlp/translateText',
    category: NodeCategory.AI,
    constructor: LanguageTranslationNode,
    label: '语言翻译',
    description: '将文本从一种语言翻译为另一种语言',
    icon: 'translate',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'translation', 'language']
  });

  // 注册语音识别节点
  registry.registerNodeType({
    type: 'ai/nlp/speechRecognition',
    category: NodeCategory.AI,
    constructor: SpeechRecognitionNode,
    label: '语音识别',
    description: '将语音转换为文本',
    icon: 'microphone',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'speech', 'recognition']
  });

  // 注册语音合成节点
  registry.registerNodeType({
    type: 'ai/nlp/speechSynthesis',
    category: NodeCategory.AI,
    constructor: SpeechSynthesisNode,
    label: '语音合成',
    description: '将文本转换为语音',
    icon: 'speaker',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'speech', 'synthesis']
  });

  // 注册意图识别节点
  registry.registerNodeType({
    type: 'ai/nlp/intentRecognition',
    category: NodeCategory.AI,
    constructor: IntentRecognitionNode,
    label: '意图识别',
    description: '识别用户输入的意图',
    icon: 'intent',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'intent', 'recognition']
  });

  // 注册对话管理节点
  registry.registerNodeType({
    type: 'ai/nlp/dialogueManagement',
    category: NodeCategory.AI,
    constructor: DialogueManagementNode,
    label: '对话管理',
    description: '管理多轮对话状态',
    icon: 'dialogue',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'dialogue', 'management']
  });

  // 注册知识图谱查询节点
  registry.registerNodeType({
    type: 'ai/nlp/knowledgeGraphQuery',
    category: NodeCategory.AI,
    constructor: KnowledgeGraphQueryNode,
    label: '知识图谱查询',
    description: '查询知识图谱获取相关信息',
    icon: 'graph',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'knowledge', 'graph', 'query']
  });

  // 注册问答系统节点
  registry.registerNodeType({
    type: 'ai/nlp/questionAnswering',
    category: NodeCategory.AI,
    constructor: QuestionAnsweringNode,
    label: '问答系统',
    description: '基于知识库的问答',
    icon: 'question',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'qa', 'question', 'answering']
  });

  // 注册关键词提取节点
  registry.registerNodeType({
    type: 'ai/nlp/keywordExtraction',
    category: NodeCategory.AI,
    constructor: KeywordExtractionNode,
    label: '关键词提取',
    description: '从文本中提取关键词',
    icon: 'keyword',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'keyword', 'extraction']
  });

  // 注册文本相似度计算节点
  registry.registerNodeType({
    type: 'ai/nlp/textSimilarity',
    category: NodeCategory.AI,
    constructor: TextSimilarityNode,
    label: '文本相似度',
    description: '计算两个文本之间的相似度',
    icon: 'similarity',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'similarity', 'comparison']
  });

  // 注册语言检测节点
  registry.registerNodeType({
    type: 'ai/nlp/languageDetection',
    category: NodeCategory.AI,
    constructor: LanguageDetectionNode,
    label: '语言检测',
    description: '自动检测文本的语言',
    icon: 'language',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'language', 'detection']
  });

  // 注册文本纠错节点
  registry.registerNodeType({
    type: 'ai/nlp/textCorrection',
    category: NodeCategory.AI,
    constructor: TextCorrectionNode,
    label: '文本纠错',
    description: '自动纠正文本中的错误',
    icon: 'correction',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'correction', 'grammar']
  });

  console.log('已注册所有AI自然语言处理节点类型');
}
