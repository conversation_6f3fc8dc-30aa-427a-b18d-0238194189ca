/**
 * 视觉脚本AI模型节点
 * 提供对不同AI模型的支持
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AIModelManager } from '../../ai/AIModelManager';
import { AIModelType } from '../../ai/AIModelType';
import { AIModelConfig } from '../../ai/AIModelConfig';
import { AIModelLoadOptions } from '../../ai/AIModelLoadOptions';
import {
  IAIModel,
  TextGenerationOptions,
  ImageGenerationOptions,
  EmotionAnalysisResult,
  TextClassificationResult,
  NamedEntityRecognitionResult,
  TextSummaryResult,
  TranslationResult
} from '../../ai/models/IAIModel';

/**
 * 加载AI模型节点
 * 加载指定类型的AI模型
 */
export class LoadAIModelNode extends AsyncNode {

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'modelType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '模型类型',
      defaultValue: AIModelType.GPT
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '模型配置',
      defaultValue: {}
    });

    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '加载选项',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '加载的模型'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '加载进度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const modelType = this.getInputValue('modelType') as string;
    const config = this.getInputValue('config') as object;
    const options = this.getInputValue('options') as object;

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建加载选项
      const loadOptions: AIModelLoadOptions = {
        ...options as AIModelLoadOptions,
        onProgress: (progress: number) => {
          this.setOutputValue('progress', progress);
        }
      };

      // 加载模型
      const model = await aiModelManager.loadModel(
        modelType as AIModelType,
        config as AIModelConfig,
        loadOptions
      );

      if (model) {
        // 设置输出值
        this.setOutputValue('model', model);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('加载AI模型失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本生成节点
 * 使用AI模型生成文本
 */
export class TextGenerationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '你好，请生成一段文本。'
    });

    this.addInput({
      name: 'maxTokens',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大令牌数',
      defaultValue: 100
    });

    this.addInput({
      name: 'temperature',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '温度 (0-1)',
      defaultValue: 0.7
    });

    this.addInput({
      name: 'stream',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否使用流式响应',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    this.addOutput({
      name: 'stream',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '流式响应'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的文本'
    });

    this.addOutput({
      name: 'streamText',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '流式响应文本'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const prompt = this.getInputValue('prompt') as string;
    const maxTokens = this.getInputValue('maxTokens') as number;
    const temperature = this.getInputValue('temperature') as number;
    const stream = this.getInputValue('stream') as boolean;

    // 检查输入值是否有效
    if (!model || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建生成选项
      const options: TextGenerationOptions = {
        maxTokens,
        temperature,
        stream
      };

      // 如果使用流式响应，添加回调
      if (stream) {
        options.onStream = (text: string) => {
          this.setOutputValue('streamText', text);
          this.triggerFlow('stream');
        };
      }

      // 生成文本
      const result = await model.generateText(prompt, options);

      if (result) {
        // 设置输出值
        this.setOutputValue('text', result);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成文本失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 图像生成节点
 * 使用AI模型生成图像
 */
export class ImageGenerationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像宽度',
      defaultValue: 512
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像高度',
      defaultValue: 512
    });

    this.addInput({
      name: 'steps',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '生成步数',
      defaultValue: 30
    });

    this.addInput({
      name: 'guidanceScale',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '引导比例',
      defaultValue: 7.5
    });

    this.addInput({
      name: 'negativePrompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '负面提示',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成进度'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'image',
      type: SocketType.DATA,
      dataType: 'blob',
      direction: SocketDirection.OUTPUT,
      description: '生成的图像'
    });

    this.addOutput({
      name: 'progressValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成进度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const prompt = this.getInputValue('prompt') as string;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const steps = this.getInputValue('steps') as number;
    const guidanceScale = this.getInputValue('guidanceScale') as number;
    const negativePrompt = this.getInputValue('negativePrompt') as string;

    // 检查输入值是否有效
    if (!model || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持图像生成
    if (!model.generateImage) {
      console.error('模型不支持图像生成');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建生成选项
      const options: ImageGenerationOptions = {
        width,
        height,
        steps,
        guidanceScale,
        negativePrompt,
        onProgress: (progress: number) => {
          this.setOutputValue('progressValue', progress);
          this.triggerFlow('progress');
        }
      };

      // 生成图像
      const result = await model.generateImage(prompt, options);

      if (result) {
        // 设置输出值
        this.setOutputValue('image', result);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成图像失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感分析节点
 * 分析文本情感
 */
export class EmotionAnalysisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'primaryEmotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主要情感'
    });

    this.addOutput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '情感强度 (0-1)'
    });

    this.addOutput({
      name: 'scores',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '情感分数映射'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持情感分析
    if (!model.analyzeEmotion) {
      console.error('模型不支持情感分析');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分析情感
      const result = await model.analyzeEmotion(text);

      if (result) {
        // 设置输出值
        this.setOutputValue('primaryEmotion', result.primaryEmotion);
        this.setOutputValue('intensity', result.intensity);
        this.setOutputValue('scores', result.scores);
        this.setOutputValue('confidence', result.confidence);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('分析情感失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 语音识别节点
 * 将语音转换为文本
 */
export class SpeechRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioData',
      type: SocketType.DATA,
      dataType: 'blob',
      direction: SocketDirection.INPUT,
      description: '音频数据'
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '识别语言',
      defaultValue: 'zh-CN'
    });

    this.addInput({
      name: 'provider',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '服务提供商',
      defaultValue: 'azure'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '识别的文本'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const audioData = this.getInputValue('audioData') as Blob;
    const language = this.getInputValue('language') as string;
    const provider = this.getInputValue('provider') as string;

    // 检查输入值是否有效
    if (!audioData) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 这里应该调用语音识别服务
      // 暂时使用模拟实现
      const result = await this.performSpeechRecognition(audioData, language, provider);

      if (result) {
        // 设置输出值
        this.setOutputValue('text', result.text);
        this.setOutputValue('confidence', result.confidence);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('语音识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 执行语音识别
   * @param audioData 音频数据
   * @param language 语言
   * @param provider 提供商
   * @returns 识别结果
   */
  private async performSpeechRecognition(audioData: Blob, language: string, provider: string): Promise<any> {
    // 这里应该集成实际的语音识别服务
    // 暂时返回模拟结果
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          text: `这是${provider}提供商使用${language}语言识别的示例文本，音频大小：${audioData.size}字节`,
          confidence: 0.95
        });
      }, 1000);
    });
  }
}

/**
 * 语音合成节点
 * 将文本转换为语音
 */
export class SpeechSynthesisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要合成的文本'
    });

    this.addInput({
      name: 'voice',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '语音类型',
      defaultValue: 'zh-CN-XiaoxiaoNeural'
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '语速',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'pitch',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音调',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '合成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '合成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'audioData',
      type: SocketType.DATA,
      dataType: 'blob',
      direction: SocketDirection.OUTPUT,
      description: '合成的音频数据'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '音频时长（秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;
    const voice = this.getInputValue('voice') as string;
    const speed = this.getInputValue('speed') as number;
    const pitch = this.getInputValue('pitch') as number;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 这里应该调用语音合成服务
      // 暂时使用模拟实现
      const result = await this.performSpeechSynthesis(text, voice, speed, pitch);

      if (result) {
        // 设置输出值
        this.setOutputValue('audioData', result.audioData);
        this.setOutputValue('duration', result.duration);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('语音合成失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 执行语音合成
   * @param text 文本
   * @param voice 语音类型
   * @param speed 语速
   * @param pitch 音调
   * @returns 合成结果
   */
  private async performSpeechSynthesis(text: string, voice: string, speed: number, pitch: number): Promise<any> {
    // 这里应该集成实际的语音合成服务
    // 暂时返回模拟结果
    return new Promise((resolve) => {
      setTimeout(() => {
        // 创建一个空的音频Blob作为示例
        const audioData = new Blob([], { type: 'audio/wav' });
        resolve({
          audioData,
          duration: (text.length * 0.1) / speed, // 根据语速调整时长
          voice,
          pitch
        });
      }, 1000);
    });
  }
}

/**
 * 翻译节点
 * 将文本从一种语言翻译为另一种语言
 */
export class TranslationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要翻译的文本'
    });

    this.addInput({
      name: 'sourceLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '源语言',
      defaultValue: 'auto'
    });

    this.addInput({
      name: 'targetLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标语言',
      defaultValue: 'en'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '翻译成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '翻译失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'translatedText',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '翻译后的文本'
    });

    this.addOutput({
      name: 'detectedLanguage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '检测到的源语言'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '翻译置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;
    const sourceLanguage = this.getInputValue('sourceLanguage') as string;
    const targetLanguage = this.getInputValue('targetLanguage') as string;

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持翻译
    if (!model.translateText) {
      console.error('模型不支持翻译功能');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 执行翻译
      const result = await model.translateText(text, targetLanguage, sourceLanguage);

      if (result) {
        // 设置输出值
        this.setOutputValue('translatedText', result.translatedText);
        this.setOutputValue('detectedLanguage', result.sourceLanguage);
        this.setOutputValue('confidence', result.confidence || 0.9);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('翻译失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本摘要节点
 * 生成文本的摘要
 */
export class TextSummarizationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要摘要的文本'
    });

    this.addInput({
      name: 'maxLength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大摘要长度',
      defaultValue: 100
    });

    this.addInput({
      name: 'summaryType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '摘要类型',
      defaultValue: 'extractive'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'summary',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的摘要'
    });

    this.addOutput({
      name: 'compressionRatio',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '压缩比例'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;
    const maxLength = this.getInputValue('maxLength') as number;

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持文本摘要
    if (!model.summarizeText) {
      console.error('模型不支持文本摘要功能');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 执行文本摘要
      const result = await model.summarizeText(text, maxLength);

      if (result) {
        // 计算压缩比例
        const compressionRatio = result.summary.length / text.length;

        // 设置输出值
        this.setOutputValue('summary', result.summary);
        this.setOutputValue('compressionRatio', compressionRatio);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本摘要失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
export class NamedEntityRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本'
    });

    this.addInput({
      name: 'entityTypes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '实体类型过滤',
      defaultValue: []
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '识别的实体列表'
    });

    this.addOutput({
      name: 'entityCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '实体数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持命名实体识别
    if (!model.recognizeEntities) {
      console.error('模型不支持命名实体识别功能');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 执行命名实体识别
      const result = await model.recognizeEntities(text);

      if (result) {
        // 设置输出值
        this.setOutputValue('entities', result.entities);
        this.setOutputValue('entityCount', result.entities.length);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('命名实体识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本分类节点
 * 对文本进行分类
 */
export class TextClassificationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分类的文本'
    });

    this.addInput({
      name: 'categories',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '分类类别',
      defaultValue: []
    });

    this.addInput({
      name: 'threshold',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '置信度阈值',
      defaultValue: 0.5
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'category',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '预测的类别'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '分类置信度'
    });

    this.addOutput({
      name: 'allScores',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '所有类别的分数'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;
    const categories = this.getInputValue('categories') as string[];

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持文本分类
    if (!model.classifyText) {
      console.error('模型不支持文本分类功能');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 执行文本分类
      const result = await model.classifyText(text, categories);

      if (result) {
        // 设置输出值
        this.setOutputValue('category', result.label);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('allScores', result.allLabels);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本分类失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 模型卸载节点
 * 卸载已加载的AI模型
 */
export class UnloadModelNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要卸载的模型'
    });

    this.addInput({
      name: 'modelId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '模型ID'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '卸载成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '卸载失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'unloaded',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功卸载'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const model = this.getInputValue('model') as IAIModel;
    const modelId = this.getInputValue('modelId') as string;

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.setOutputValue('unloaded', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      let success = false;

      if (model) {
        // 通过模型实例卸载
        success = aiModelManager.unloadModel(model.getId());
      } else if (modelId) {
        // 通过模型ID卸载
        success = aiModelManager.unloadModel(modelId);
      }

      // 设置输出值
      this.setOutputValue('unloaded', success);

      if (success) {
        this.triggerFlow('success');
      } else {
        this.triggerFlow('fail');
      }

      return success;
    } catch (error) {
      console.error('卸载模型失败:', error);
      this.setOutputValue('unloaded', false);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 批量推理节点
 * 对多个输入进行批量推理
 */
export class BatchInferenceNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'inputs',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '输入数据数组'
    });

    this.addInput({
      name: 'batchSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '批次大小',
      defaultValue: 10
    });

    this.addInput({
      name: 'inferenceType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '推理类型',
      defaultValue: 'text'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '推理成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '推理失败'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '进度更新'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'results',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '推理结果数组'
    });

    this.addOutput({
      name: 'progressValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '进度值 (0-1)'
    });

    this.addOutput({
      name: 'processedCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '已处理数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    const model = this.getInputValue('model') as IAIModel;
    const inputs = this.getInputValue('inputs') as any[];
    const batchSize = this.getInputValue('batchSize') as number;
    const inferenceType = this.getInputValue('inferenceType') as string;

    // 检查输入值是否有效
    if (!model || !Array.isArray(inputs) || inputs.length === 0) {
      this.setOutputValue('results', []);
      this.setOutputValue('processedCount', 0);
      this.triggerFlow('fail');
      return false;
    }

    try {
      const results: any[] = [];
      const totalInputs = inputs.length;
      let processedCount = 0;

      // 分批处理
      for (let i = 0; i < totalInputs; i += batchSize) {
        const batch = inputs.slice(i, i + batchSize);
        const batchResults = await this.processBatch(model, batch, inferenceType);

        results.push(...batchResults);
        processedCount += batch.length;

        // 更新进度
        const progress = processedCount / totalInputs;
        this.setOutputValue('progressValue', progress);
        this.setOutputValue('processedCount', processedCount);
        this.triggerFlow('progress');
      }

      // 设置最终输出值
      this.setOutputValue('results', results);
      this.setOutputValue('processedCount', processedCount);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('批量推理失败:', error);
      this.setOutputValue('results', []);
      this.setOutputValue('processedCount', 0);
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 处理单个批次
   * @param model 模型
   * @param batch 批次数据
   * @param inferenceType 推理类型
   * @returns 批次结果
   */
  private async processBatch(model: IAIModel, batch: any[], inferenceType: string): Promise<any[]> {
    const results: any[] = [];

    for (const input of batch) {
      try {
        let result: any;

        switch (inferenceType) {
          case 'text':
            result = await model.generateText(input);
            break;
          case 'emotion':
            if (model.analyzeEmotion) {
              result = await model.analyzeEmotion(input);
            }
            break;
          case 'classification':
            if (model.classifyText) {
              result = await model.classifyText(input);
            }
            break;
          case 'ner':
            if (model.recognizeEntities) {
              result = await model.recognizeEntities(input);
            }
            break;
          case 'summary':
            if (model.summarizeText) {
              result = await model.summarizeText(input);
            }
            break;
          default:
            result = await model.generateText(input);
        }

        results.push(result);
      } catch (error) {
        console.error('单个推理失败:', error);
        results.push(null);
      }
    }

    return results;
  }
}

/**
 * 注册AI模型节点
 * @param registry 节点注册表
 */
export function registerAIModelNodes(registry: NodeRegistry): void {
  // 注册加载AI模型节点
  registry.registerNodeType({
    type: 'ai/model/load',
    category: NodeCategory.AI,
    constructor: LoadAIModelNode,
    label: '加载AI模型',
    description: '加载指定类型的AI模型',
    icon: 'model',
    color: '#673AB7',
    tags: ['ai', 'model', 'load']
  });

  // 注册文本生成节点
  registry.registerNodeType({
    type: 'ai/model/generateText',
    category: NodeCategory.AI,
    constructor: TextGenerationNode,
    label: '生成文本',
    description: '使用AI模型生成文本',
    icon: 'text',
    color: '#673AB7',
    tags: ['ai', 'model', 'text', 'generate']
  });

  // 注册图像生成节点
  registry.registerNodeType({
    type: 'ai/model/generateImage',
    category: NodeCategory.AI,
    constructor: ImageGenerationNode,
    label: '生成图像',
    description: '使用AI模型生成图像',
    icon: 'image',
    color: '#673AB7',
    tags: ['ai', 'model', 'image', 'generate']
  });

  // 注册情感分析节点
  registry.registerNodeType({
    type: 'ai/emotion/analyze',
    category: NodeCategory.AI,
    constructor: EmotionAnalysisNode,
    label: '情感分析',
    description: '分析文本情感',
    icon: 'emotion',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'analyze']
  });

  // 注册语音识别节点
  registry.registerNodeType({
    type: 'ai/speech/recognition',
    category: NodeCategory.AI,
    constructor: SpeechRecognitionNode,
    label: '语音识别',
    description: '将语音转换为文本',
    icon: 'microphone',
    color: '#673AB7',
    tags: ['ai', 'speech', 'recognition', 'voice']
  });

  // 注册语音合成节点
  registry.registerNodeType({
    type: 'ai/speech/synthesis',
    category: NodeCategory.AI,
    constructor: SpeechSynthesisNode,
    label: '语音合成',
    description: '将文本转换为语音',
    icon: 'speaker',
    color: '#673AB7',
    tags: ['ai', 'speech', 'synthesis', 'tts']
  });

  // 注册翻译节点
  registry.registerNodeType({
    type: 'ai/text/translate',
    category: NodeCategory.AI,
    constructor: TranslationNode,
    label: '文本翻译',
    description: '将文本从一种语言翻译为另一种语言',
    icon: 'translate',
    color: '#673AB7',
    tags: ['ai', 'text', 'translate', 'language']
  });

  // 注册文本摘要节点
  registry.registerNodeType({
    type: 'ai/text/summarize',
    category: NodeCategory.AI,
    constructor: TextSummarizationNode,
    label: '文本摘要',
    description: '生成文本的摘要',
    icon: 'summary',
    color: '#673AB7',
    tags: ['ai', 'text', 'summarize', 'summary']
  });

  // 注册命名实体识别节点
  registry.registerNodeType({
    type: 'ai/text/ner',
    category: NodeCategory.AI,
    constructor: NamedEntityRecognitionNode,
    label: '命名实体识别',
    description: '识别文本中的命名实体',
    icon: 'entity',
    color: '#673AB7',
    tags: ['ai', 'text', 'ner', 'entity']
  });

  // 注册文本分类节点
  registry.registerNodeType({
    type: 'ai/text/classify',
    category: NodeCategory.AI,
    constructor: TextClassificationNode,
    label: '文本分类',
    description: '对文本进行分类',
    icon: 'classify',
    color: '#673AB7',
    tags: ['ai', 'text', 'classify', 'classification']
  });

  // 注册模型卸载节点
  registry.registerNodeType({
    type: 'ai/model/unload',
    category: NodeCategory.AI,
    constructor: UnloadModelNode,
    label: '卸载模型',
    description: '卸载已加载的AI模型',
    icon: 'unload',
    color: '#673AB7',
    tags: ['ai', 'model', 'unload', 'management']
  });

  // 注册批量推理节点
  registry.registerNodeType({
    type: 'ai/model/batchInference',
    category: NodeCategory.AI,
    constructor: BatchInferenceNode,
    label: '批量推理',
    description: '对多个输入进行批量推理',
    icon: 'batch',
    color: '#673AB7',
    tags: ['ai', 'model', 'batch', 'inference']
  });
}
